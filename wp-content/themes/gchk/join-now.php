<?php

/**
 * Template name: Join Now
 */

// Start session to handle messages
if (session_status() == PHP_SESSION_NONE) {
  session_start();
}

// Get form errors, data, and messages from session
$form_errors = isset($_SESSION['form_errors']) ? $_SESSION['form_errors'] : array();
$form_data = isset($_SESSION['form_data']) ? $_SESSION['form_data'] : array();
$form_error = isset($_SESSION['form_error']) ? $_SESSION['form_error'] : '';
$form_success = isset($_SESSION['form_success']) ? $_SESSION['form_success'] : '';

// Clear messages after displaying them
if (!empty($form_errors)) {
  unset($_SESSION['form_errors']);
}
if (!empty($form_data)) {
  unset($_SESSION['form_data']);
}
if (!empty($form_error)) {
  unset($_SESSION['form_error']);
}
if (!empty($form_success)) {
  unset($_SESSION['form_success']);
}

get_header(); ?>

<style>
  .alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
  }

  .alert-success {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
  }

  .alert-danger {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
  }

  .form-error {
    color: #a94442;
    font-size: 12px;
    margin-top: 5px;
  }

  .has-error input,
  .has-error select,
  .has-error textarea {
    border-color: #a94442;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
  }

  .has-error input:focus,
  .has-error select:focus,
  .has-error textarea:focus {
    border-color: #843534;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #ce8483;
  }

  /* Real-time validation styles */
  .field-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: none;
    font-weight: 500;
  }

  .field-error.show {
    display: block;
  }

  .field-valid {
    border-color: #28a745 !important;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px rgba(40, 167, 69, 0.3) !important;
  }

  .field-invalid {
    border-color: #dc3545 !important;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px rgba(220, 53, 69, 0.3) !important;
  }

  .form-group {
    position: relative;
    margin-bottom: 20px;
  }
</style>

<section class="pagetitle">
  <div class="container">
    <h1>Membership Registration Form</h1>
  </div>
</section>
<section class="section-membership">
  <div class="container">

    <?php if (!empty($form_success)): ?>
      <div class="alert alert-success">
        <strong>Success!</strong> <?php echo esc_html($form_success); ?>
      </div>
    <?php endif; ?>

    <?php if (!empty($form_error)): ?>
      <div class="alert alert-danger">
        <strong>Error!</strong> <?php echo esc_html($form_error); ?>
      </div>
    <?php endif; ?>

    <?php if (!empty($form_errors)): ?>
      <div class="alert alert-danger">
        <strong>Please correct the following errors:</strong>
        <ul style="margin-top: 10px; margin-bottom: 0;">
          <?php foreach ($form_errors as $field => $error): ?>
            <li><?php echo esc_html($error); ?></li>
          <?php endforeach; ?>
        </ul>
      </div>
    <?php endif; ?>

    <!-- Custom Membership Registration Form -->
    <form id="membership-form" method="post" enctype="multipart/form-data" action="<?php echo esc_url(admin_url('admin-post.php')); ?>">
      <?php wp_nonce_field('membership_form_nonce', 'membership_nonce'); ?>
      <input type="hidden" name="action" value="handle_membership_form">

      <h3><strong>Section 1 : PERSONAL INFORMATION</strong></h3>
      <div class="row">
        <div class="col-md-5 form-group <?php echo isset($form_errors['full-name']) ? 'has-error' : ''; ?>">
          <p>Full Name :</p>
          <input type="text" name="full-name" class="form-control" value="<?php echo isset($form_data['full_name']) ? esc_attr($form_data['full_name']) : ''; ?>" required>
          <?php if (isset($form_errors['full-name'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['full-name']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="full-name-error"></div>
        </div>
        <div class="col-md-5 form-group <?php echo isset($form_errors['birth-date']) ? 'has-error' : ''; ?>">
          <p>Date of Birth : </p>
          <input type="date" name="birth-date" class="form-control" value="<?php echo isset($form_data['birth_date']) ? esc_attr($form_data['birth_date']) : ''; ?>" required>
          <?php if (isset($form_errors['birth-date'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['birth-date']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="birth-date-error"></div>
        </div>
        <div class="col-md-2 form-group <?php echo isset($form_errors['gender']) ? 'has-error' : ''; ?>">
          <p>Gender : </p>
          <div class="radio-check">
            <label><input type="radio" name="gender" value="M" <?php echo (isset($form_data['gender']) && $form_data['gender'] == 'M') ? 'checked' : ''; ?> required> M</label>
            <label><input type="radio" name="gender" value="F" <?php echo (isset($form_data['gender']) && $form_data['gender'] == 'F') ? 'checked' : ''; ?> required> F</label>
          </div>
          <?php if (isset($form_errors['gender'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['gender']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="gender-error"></div>
        </div>
      </div>

      <h3><strong>Section 2 : CONTACT INFORMATION</strong></h3>
      <div class="row">
        <div class="col-md-6 form-group <?php echo isset($form_errors['phone-number']) ? 'has-error' : ''; ?>">
          <p>Phone Number : </p>
          <input type="tel" name="phone-number" class="form-control" value="<?php echo isset($form_data['phone_number']) ? esc_attr($form_data['phone_number']) : ''; ?>" required>
          <?php if (isset($form_errors['phone-number'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['phone-number']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="phone-number-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['email-address']) ? 'has-error' : ''; ?>">
          <p>Email Address : </p>
          <input type="email" name="email-address" class="form-control" value="<?php echo isset($form_data['email_address']) ? esc_attr($form_data['email_address']) : ''; ?>" required>
          <?php if (isset($form_errors['email-address'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['email-address']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="email-address-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['address']) ? 'has-error' : ''; ?>">
          <p>Address : </p>
          <input type="text" name="address" class="form-control" value="<?php echo isset($form_data['address']) ? esc_attr($form_data['address']) : ''; ?>" required>
          <small>(Street, City, State/Province, Country, Postal Code)</small>
          <?php if (isset($form_errors['address'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['address']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="address-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['country-residence']) ? 'has-error' : ''; ?>">
          <p>Country of Residence :</p>
          <input type="text" name="country-residence" class="form-control" value="<?php echo isset($form_data['country_residence']) ? esc_attr($form_data['country_residence']) : ''; ?>" required>
          <?php if (isset($form_errors['country-residence'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['country-residence']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="country-residence-error"></div>
        </div>
      </div>

      <h3><strong>Section 3 : GOVERNMENT IDENTIFICATION</strong></h3>
      <div class="row">
        <div class="col-md-4 form-group">
          <p>Identification Number : </p>
          <input type="text" name="identification-number" class="form-control" required>
          <div class="field-error" id="identification-number-error"></div>
        </div>
        <div class="col-md-4 form-group">
          <p>Issuing Country :</p>
          <input type="text" name="issuing-country" class="form-control" required>
          <div class="field-error" id="issuing-country-error"></div>
        </div>
        <div class="col-md-4 form-group">
          <p>Expiration Date : </p>
          <input type="date" name="expiration-date" class="form-control" min="2024-12-07" required>
          <div class="field-error" id="expiration-date-error"></div>
        </div>
        <div class="col-md-6 form-group">
          <p>National ID Number : </p>
          <input type="text" name="id-number" class="form-control" required>
          <div class="field-error" id="id-number-error"></div>
        </div>
        <div class="col-md-6 form-group">
          <p>Passport Number :</p>
          <input type="text" name="passport-number" class="form-control" required>
          <div class="field-error" id="passport-number-error"></div>
        </div>
        <div class="col-md-6 form-group">
          <p>Place of issue : </p>
          <input type="text" name="place-issue" class="form-control" required>
          <div class="field-error" id="place-issue-error"></div>
        </div>
        <div class="col-md-6 form-group">
          <p>Date of issue : </p>
          <input type="date" name="date-issue" class="form-control" required>
          <div class="field-error" id="date-issue-error"></div>
        </div>
      </div>

      <h3><strong>Section 4 : EMERGENCY CONTACT INFORMATION</strong></h3>
      <div class="row">
        <div class="col-md-6 form-group">
          <p>Contact Name :</p>
          <input type="text" name="contact-name" class="form-control" required>
          <div class="field-error" id="contact-name-error"></div>
        </div>
        <div class="col-md-6 form-group">
          <p>Contact Relationship :</p>
          <input type="text" name="contact-relationship" class="form-control" required>
          <div class="field-error" id="contact-relationship-error"></div>
        </div>
        <div class="col-md-6 form-group">
          <p>Contact Phone Number :</p>
          <input type="tel" name="contact-number" class="form-control" required>
          <div class="field-error" id="contact-number-error"></div>
        </div>
        <div class="col-md-6 form-group">
          <p>Contact Email Address :</p>
          <input type="email" name="contact-email" class="form-control" required>
          <div class="field-error" id="contact-email-error"></div>
        </div>
      </div>

      <h3><strong>Section 5 : PROFESSIONAL INFORMATION</strong></h3>
      <div class="row">
        <div class="col-md-4 form-group">
          <p>Occupation :</p>
          <input type="text" name="occupation" class="form-control" required>
          <div class="field-error" id="occupation-error"></div>
        </div>
        <div class="col-md-4 form-group">
          <p>Employer :</p>
          <input type="text" name="employer" class="form-control" required>
          <div class="field-error" id="employer-error"></div>
        </div>
        <div class="col-md-4 form-group">
          <p>Professional Affiliations:</p>
          <input type="text" name="professional-aff" class="form-control" required>
          <div class="field-error" id="professional-aff-error"></div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6 form-group <?php echo isset($form_errors['photo-file']) ? 'has-error' : ''; ?>">
          <h3><strong>Section 6 : RECENT PHOTOGRAPH</strong></h3>
          <p>Attach your Passport-sized Photo</p>
          <input type="file" name="photo-file" accept="image/*" required>
          <small>Maximum file size: 2MB</small>
          <?php if (isset($form_errors['photo-file'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['photo-file']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="photo-file-error"></div>
        </div>
        <div class="col-md-6 form-group <?php echo isset($form_errors['signature-file']) ? 'has-error' : ''; ?>">
          <h3><strong>Section 7 : CONSENT & SIGNATURE</strong></h3>
          <p>Consent: I agree to share my information with the consulate/embassy for membership purposes.</p>
          <p>Attach your Digital Signature:</p>
          <input type="file" name="signature-file" accept="image/*" required>
          <small>Maximum file size: 1MB</small>
          <?php if (isset($form_errors['signature-file'])): ?>
            <div class="form-error"><?php echo esc_html($form_errors['signature-file']); ?></div>
          <?php endif; ?>
          <div class="field-error" id="signature-file-error"></div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <input type="submit" value="Submit" class="btn btn-primary">
        </div>
      </div>
    </form>

    <article class="join-article">
      <?php echo get_the_post_thumbnail(get_the_ID(), 'full'); ?>
    </article>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('membership-form');

    // Validation rules
    const validationRules = {
      'full-name': {
        required: true,
        minLength: 2,
        pattern: /^[a-zA-Z\s]+$/,
        message: 'Full name must contain only letters and spaces, minimum 2 characters'
      },
      'birth-date': {
        required: true,
        validate: function(value) {
          const date = new Date(value);
          const today = new Date();
          const age = today.getFullYear() - date.getFullYear();
          return age >= 18 && age <= 100;
        },
        message: 'You must be between 18 and 100 years old'
      },
      'gender': {
        required: true,
        message: 'Please select your gender'
      },
      'phone-number': {
        required: true,
        pattern: /^[\+]?[1-9][\d]{0,15}$/,
        message: 'Please enter a valid phone number'
      },
      'email-address': {
        required: true,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: 'Please enter a valid email address'
      },
      'address': {
        required: true,
        minLength: 10,
        message: 'Address must be at least 10 characters long'
      },
      'country-residence': {
        required: true,
        minLength: 2,
        pattern: /^[a-zA-Z\s]+$/,
        message: 'Country must contain only letters and spaces'
      },
      'identification-number': {
        required: true,
        minLength: 5,
        message: 'Identification number must be at least 5 characters'
      },
      'issuing-country': {
        required: true,
        minLength: 2,
        pattern: /^[a-zA-Z\s]+$/,
        message: 'Issuing country must contain only letters and spaces'
      },
      'expiration-date': {
        required: true,
        validate: function(value) {
          const date = new Date(value);
          const today = new Date();
          return date > today;
        },
        message: 'Expiration date must be in the future'
      },
      'id-number': {
        required: true,
        minLength: 5,
        message: 'National ID number must be at least 5 characters'
      },
      'passport-number': {
        required: true,
        minLength: 6,
        message: 'Passport number must be at least 6 characters'
      },
      'place-issue': {
        required: true,
        minLength: 2,
        pattern: /^[a-zA-Z\s]+$/,
        message: 'Place of issue must contain only letters and spaces'
      },
      'date-issue': {
        required: true,
        validate: function(value) {
          const date = new Date(value);
          const today = new Date();
          return date <= today;
        },
        message: 'Date of issue cannot be in the future'
      },
      'contact-name': {
        required: true,
        minLength: 2,
        pattern: /^[a-zA-Z\s]+$/,
        message: 'Contact name must contain only letters and spaces'
      },
      'contact-relationship': {
        required: true,
        minLength: 2,
        message: 'Contact relationship must be at least 2 characters'
      },
      'contact-number': {
        required: true,
        pattern: /^[\+]?[1-9][\d]{0,15}$/,
        message: 'Please enter a valid contact phone number'
      },
      'contact-email': {
        required: true,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: 'Please enter a valid contact email address'
      },
      'occupation': {
        required: true,
        minLength: 2,
        message: 'Occupation must be at least 2 characters'
      },
      'employer': {
        required: true,
        minLength: 2,
        message: 'Employer must be at least 2 characters'
      },
      'professional-aff': {
        required: true,
        minLength: 2,
        message: 'Professional affiliations must be at least 2 characters'
      },
      'photo-file': {
        required: true,
        validate: function(input) {
          const file = input.files[0];
          if (!file) return false;
          if (file.size > 2 * 1024 * 1024) return false;
          return file.type.startsWith('image/');
        },
        message: 'Please upload a valid image file (max 2MB)'
      },
      'signature-file': {
        required: true,
        validate: function(input) {
          const file = input.files[0];
          if (!file) return false;
          if (file.size > 1 * 1024 * 1024) return false;
          return file.type.startsWith('image/');
        },
        message: 'Please upload a valid image file (max 1MB)'
      }
    };

    // Validation function
    function validateField(fieldName, value, input) {
      const rule = validationRules[fieldName];
      if (!rule) return true;

      // Check required
      if (rule.required && (!value || value.trim() === '')) {
        return false;
      }

      // Check minimum length
      if (rule.minLength && value.length < rule.minLength) {
        return false;
      }

      // Check pattern
      if (rule.pattern && !rule.pattern.test(value)) {
        return false;
      }

      // Check custom validation
      if (rule.validate && !rule.validate(input || value)) {
        return false;
      }

      return true;
    }

    // Show/hide error message
    function showError(fieldName, message) {
      const errorElement = document.getElementById(fieldName + '-error');
      const input = document.querySelector(`[name="${fieldName}"]`);

      if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.add('show');
      }

      if (input) {
        input.classList.remove('field-valid');
        input.classList.add('field-invalid');
      }
    }

    function hideError(fieldName) {
      const errorElement = document.getElementById(fieldName + '-error');
      const input = document.querySelector(`[name="${fieldName}"]`);

      if (errorElement) {
        errorElement.classList.remove('show');
      }

      if (input) {
        input.classList.remove('field-invalid');
        input.classList.add('field-valid');
      }
    }

    // Add event listeners for real-time validation
    Object.keys(validationRules).forEach(fieldName => {
      const input = document.querySelector(`[name="${fieldName}"]`);
      if (!input) return;

      // Handle different input types
      if (input.type === 'radio') {
        const radioInputs = document.querySelectorAll(`[name="${fieldName}"]`);
        radioInputs.forEach(radio => {
          radio.addEventListener('change', function() {
            const isValid = validateField(fieldName, this.value, this);
            if (isValid) {
              hideError(fieldName);
            } else {
              showError(fieldName, validationRules[fieldName].message);
            }
          });
        });
      } else if (input.type === 'file') {
        input.addEventListener('change', function() {
          const isValid = validateField(fieldName, this.value, this);
          if (isValid) {
            hideError(fieldName);
          } else {
            showError(fieldName, validationRules[fieldName].message);
          }
        });
      } else {
        // Text, email, tel, date inputs
        input.addEventListener('keyup', function() {
          const isValid = validateField(fieldName, this.value, this);
          if (isValid) {
            hideError(fieldName);
          } else {
            showError(fieldName, validationRules[fieldName].message);
          }
        });

        input.addEventListener('blur', function() {
          const isValid = validateField(fieldName, this.value, this);
          if (isValid) {
            hideError(fieldName);
          } else {
            showError(fieldName, validationRules[fieldName].message);
          }
        });
      }
    });

    // Form submission validation
    form.addEventListener('submit', function(e) {
      let isFormValid = true;
      let firstErrorField = null;

      // Validate all fields
      Object.keys(validationRules).forEach(fieldName => {
        const input = document.querySelector(`[name="${fieldName}"]`);
        if (!input) return;

        let value;
        if (input.type === 'radio') {
          const checkedRadio = document.querySelector(`[name="${fieldName}"]:checked`);
          value = checkedRadio ? checkedRadio.value : '';
        } else {
          value = input.value;
        }

        const isValid = validateField(fieldName, value, input);
        if (!isValid) {
          showError(fieldName, validationRules[fieldName].message);
          isFormValid = false;
          if (!firstErrorField) {
            firstErrorField = input;
          }
        } else {
          hideError(fieldName);
        }
      });

      if (!isFormValid) {
        e.preventDefault();

        // Force immediate display of errors
        setTimeout(() => {
          // Re-trigger error display for all invalid fields
          Object.keys(validationRules).forEach(fieldName => {
            const input = document.querySelector(`[name="${fieldName}"]`);
            if (!input) return;

            let value;
            if (input.type === 'radio') {
              const checkedRadio = document.querySelector(`[name="${fieldName}"]:checked`);
              value = checkedRadio ? checkedRadio.value : '';
            } else {
              value = input.value;
            }

            const isValid = validateField(fieldName, value, input);
            if (!isValid) {
              showError(fieldName, validationRules[fieldName].message);
            }
          });

          // Scroll to first error field after errors are displayed
          if (firstErrorField) {
            firstErrorField.scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            });

            // Focus after scroll completes
            setTimeout(() => {
              firstErrorField.focus();
              // Trigger a visual highlight
              firstErrorField.style.transition = 'all 0.3s ease';
              firstErrorField.style.transform = 'scale(1.02)';
              setTimeout(() => {
                firstErrorField.style.transform = 'scale(1)';
              }, 300);
            }, 500);
          }
        }, 100);

        return false;
      }

      // Show loading state
      const submitBtn = this.querySelector('input[type="submit"]');
      submitBtn.value = 'Submitting...';
      submitBtn.disabled = true;
    });
  });
</script>

<?php get_footer(); ?>